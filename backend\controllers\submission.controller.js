/**
 * Submission controller
 * Handles HTTP requests for submission-related endpoints
 */
import asyncHand<PERSON> from "express-async-handler";
import { StatusCodes } from "http-status-codes";
import SubmissionService from "../services/submissionService.js";
import {
  headersSchema,
  submissionRequestSchema,
} from "../utils/validationSchemas.js";
import User from "../models/user.model.js";
import Question from "../models/question.model.js";
import SolutionTest from "../models/solution_test.model.js";
// import
/**
 * Run code for a problem
 * @route POST /api/v1/leetcode/run-code/:questionTitleSlug
 * @access Public
 */
const runCode = asyncHandler(async (req, res) => {
  const { questionTitleSlug } = req.params;

  // Validate submission data
  const { error, value: submissionData } = submissionRequestSchema.validate(
    req.body
  );
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid submission data: ${error.message}`);
  }

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Run the code
  const runResult = await SubmissionService.runCode(
    questionTitleSlug,
    submissionData,
    headers
  );

  res.status(StatusCodes.OK).json(runResult);
});

/**
 * Submit code for a problem
 * @route POST /api/v1/leetcode/submit/:questionTitleSlug
 * @access Public
 */
const submitSolution = asyncHandler(async (req, res) => {
  const { questionTitleSlug } = req.params;

  // Validate submission data
  const { error, value: submissionData } = submissionRequestSchema.validate(
    req.body
  );
  if (error) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid submission data: ${error.message}`);
  }

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  try {
    // Submit the solution and get the submission ID
    const submissionId = await SubmissionService.submitSolutionAndGetId(
      questionTitleSlug,
      submissionData,
      headers
    );

    // Set the submission ID in the response headers
    res.setHeader("X-Submission-ID", submissionId);

    // Return the submission ID to the client
    res.status(StatusCodes.OK).json({
      submission_id: submissionId,
      state: "PENDING",
    });
  } catch (error) {
    res.status(StatusCodes.INTERNAL_SERVER_ERROR);
    throw new Error(`Failed to submit solution: ${error.message}`);
  }
});

/**
 * Check submission status
 * @route GET /api/v1/leetcode/submissions/:submissionId/check
 * @access Public
 */
const checkSubmission = asyncHandler(async (req, res) => {
  const { submissionId } = req.params;

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Check the submission
  const submissionStatus = await SubmissionService.checkSubmission(
    submissionId,
    headers
  );

  // Set the submission ID in the response headers
  res.setHeader("X-Submission-ID", submissionId);

  // Log the submission status
  console.log(
    `Submission status for ID ${submissionId}:`,
    JSON.stringify(submissionStatus)
  );

  res.status(StatusCodes.OK).json(submissionStatus);
});

const afterSubmission = asyncHandler(async (req, res) => {
  try {
    //userid will be fetched from the cookie
    // questionTitle will be fetched from the request body
    const { userId, questionTitle, code, language, approach} = req.body;

    const user = await User.findById(userId);
    console.log("User:", user);
    if (!user)
      return res
        .status(404)
        .json({ success: false, message: "User not found" });

    const question = await Question.findOne({ title: questionTitle });
    console.log("Question:", question);
    if (!question)
      return res
        .status(404)
        .json({ success: false, message: "Question not found" });

    // Step 2(i): Create solution doc
    const newSolution = await SolutionTest.create({
      user: user._id,
      question: question._id,
      code,
      language,
      approach
    });

    await newSolution.save();
    // Step 2(ii): Append user to question's userList if not already present
    if (!question.userList.includes(user._id)) {
      question.userList.push(user._id);
      await question.save();
    }

    // Step 2(iii): Update user's sol map
    const mapKey = questionTitle; 
    // const existingSolutions = []
    const existingSolutions = user.sol.get(mapKey);

    if (existingSolutions) {
      user.sol.set(mapKey, [...existingSolutions, newSolution._id]);
    } else {
      user.sol.set(mapKey, [newSolution._id]);
    }

    await user.save();

    res.status(201).json({ success: true, solution: newSolution });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});
/**
 * Check run code result
 * @route GET /api/v1/leetcode/run-code/check/:interpretId
 * @access Public
 */
const checkRunResult = asyncHandler(async (req, res) => {
  const { interpretId } = req.params;

  // Extract headers from request
  const headers = {
    cookie: req.headers.cookie,
    csrfToken: req.headers["x-csrftoken"],
    userAgent: req.headers["user-agent"],
    origin: req.headers.origin,
    referer: req.headers.referer,
  };

  // Validate headers
  const { error: headersError } = headersSchema.validate(headers);
  if (headersError) {
    res.status(StatusCodes.BAD_REQUEST);
    throw new Error(`Invalid headers: ${headersError.message}`);
  }

  // Check the run result
  const runResult = await SubmissionService.checkRunResult(
    interpretId,
    headers
  );

  res.status(StatusCodes.OK).json(runResult);
});

/**
 * Get latest submission ID
 * @route GET /api/v1/leetcode/submissions/latest
 * @access Public
 */
const getLatestSubmissionId = asyncHandler(async (req, res) => {
  // This is a placeholder endpoint that would normally fetch the latest submission ID
  // Since we don't have a way to get this directly, we'll return a 404
  res.status(StatusCodes.NOT_FOUND);
  throw new Error("Not implemented");
});

export {
  runCode,
  submitSolution,
  checkSubmission,
  afterSubmission,
  checkRunResult,
  getLatestSubmissionId,
};
