import { Schema, model } from "mongoose"
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'
import { stat } from "fs"
import { type } from "os"

const questionSchema = new Schema({
    title: {
        type: String,
        required: [true, 'Please enter the question title'],
        trim: true,
        maxlength: [100, 'Title cannot exceed 100 characters'],
        minlength: [5, 'Title must be at least 5 characters long']
    },
   questionLink:{
        type: String,
        required: true,
        trim: true,
        match: [
            /^(https?:\/\/)?([\w-]+(\.[\w-]+)+)(\/[\w- .\/?%&=]*)?$/,
            'Please fill in a valid URL',
        ]
    },
    difficulty: {
        type: String,
        required: [true, 'Please specify the difficulty level'],
        enum: ['easy', 'medium', 'hard'],
        default: 'medium'
    },
    tags: [{
        type: String,
        required: true,
        enum: ['array', 'string', 'linked list', 'tree', 'graph', 'dynamic programming', 'sorting', 'searching', 'hashing', 'greedy', 'backtracking']
    }],
    userList:{
        type: [Schema.Types.ObjectId],
        ref: 'User',
        required: true
    }
}, {
    timestamps: true
})


const Question = model('Question', questionSchema)
export default Question