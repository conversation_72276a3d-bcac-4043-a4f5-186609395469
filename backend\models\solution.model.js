import { Schema, model } from "mongoose"

const solutionSchema = new Schema({
    user: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: false // Allow null for anonymous submissions
    },
    username: {
        type: String,
        required: true
    },
    // Problem information
    problemId: {
        type: String,
        required: true
    },
    problemTitle: {
        type: String,
        required: true
    },
    problemSlug: {
        type: String,
        required: true
    },
    difficulty: {
        type: String,
        enum: ['Easy', 'Medium', 'Hard'],
        required: true
    },
    // Solution details
    code: {
        type: String,
        required: [true, 'Please enter your solution code'],
        trim: true,
        minlength: [10, 'Code must be at least 10 characters long']
    },
    language: {
        type: String,
        required: [true, 'Please specify the programming language'],
        enum: ['javascript', 'python3', 'java', 'cpp', 'c', 'csharp', 'ruby', 'go', 'php'],
        default: 'cpp'
    },
    approach: {
        type: String,
        trim: true
    },
    // Submission results from LeetCode
    submissionId: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['Accepted', 'Wrong Answer', 'Time Limit Exceeded', 'Memory Limit Exceeded', 'Runtime Error', 'Compile Error', 'Output Limit Exceeded'],
        required: true
    },
    runtime: {
        type: String
    },
    memory: {
        type: String
    },
    runtimePercentile: {
        type: Number
    },
    memoryPercentile: {
        type: Number
    },
    totalCorrect: {
        type: Number
    },
    totalTestcases: {
        type: Number
    },
    // Additional metadata
    isAccepted: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
})

const Solution = model('Solution', solutionSchema)
export default Solution