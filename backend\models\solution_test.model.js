import { Schema, model } from "mongoose"
// import bcrypt from 'bcryptjs'
// import jwt from 'jsonwebtoken'
// import crypto from 'crypto'
// import { stat } from "fs"
// import { type } from "os"

const solutionTestSchema = new Schema({
    user: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    question: {
        type: Schema.Types.ObjectId,
        ref: 'Question',
        required: true
    },
    code: {
        type: String,
        required: [true, 'Please enter your solution code'],
        trim: true,
        unique: true,
        minlength: [10, 'Code must be at least 10 characters long']
    },
    language:{
        type: String,
        required: [true, 'Please specify the programming language'],
        enum: ['javascript', 'python', 'java', 'cpp', 'c#', 'ruby', 'go', 'php'],
        default: 'c++'
    },
    approach:{
        type: String,
    }
}, {
    timestamps: true
})

const SolutionTest = model('SolutionTest', solutionTestSchema)
export default SolutionTest