export const reviewData = [
  {
    id: 'arrays',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    review: 'This topic really helped me master the basics!',
    icon: 'FaListOl',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
  },
  {
    id: 'strings',
    name: '<PERSON><PERSON>',
    review: 'Strings section was super intuitive and well-explained.',
    icon: 'FaCode',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
  },
  {
    id: 'dynamic-programming',
    name: '<PERSON><PERSON><PERSON>',
    review: 'Dynamic Programming finally makes sense thanks to Big O Web!',
    icon: 'FaLayerGroup',
    image: 'https://randomuser.me/api/portraits/men/15.jpg',
  },
  {
    id: 'graphs',
    name: '<PERSON><PERSON>',
    review: 'I struggled with Graphs until I found these visual solutions!',
    icon: 'FaNetworkWired',
    image: 'https://randomuser.me/api/portraits/women/19.jpg',
  },
  {
    id: 'trees',
    name: '<PERSON><PERSON><PERSON>',
    review: 'Tree problems are now a cakewalk. The tips were golden.',
    icon: 'FaTree',
    image: 'https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg',
  },
  {
    id: 'backtracking',
    name: 'Vaibhav Sangwan',
    review: 'Backtracking examples were well chosen and built my confidence.',
    icon: 'FaRandom',
    image: 'https://t3.ftcdn.net/jpg/02/43/12/34/360_F_243123463_zTooub557xEWABDLk0jJklDyLSGl2jrr.jpg',
  }
];
