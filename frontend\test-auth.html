<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Authentication Test</h1>
    
    <h2>Register</h2>
    <form id="registerForm">
        <div class="form-group">
            <label for="regFullName">Full Name:</label>
            <input type="text" id="regFullName" value="Test User" required>
        </div>
        <div class="form-group">
            <label for="regEmail">Email:</label>
            <input type="email" id="regEmail" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="regPassword">Password:</label>
            <input type="password" id="regPassword" value="password123" required>
        </div>
        <button type="submit">Register</button>
    </form>
    
    <h2>Login</h2>
    <form id="loginForm">
        <div class="form-group">
            <label for="loginEmail">Email:</label>
            <input type="email" id="loginEmail" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label for="loginPassword">Password:</label>
            <input type="password" id="loginPassword" value="password123" required>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <button onclick="testProfile()">Get Profile</button>
    <button onclick="testLogout()">Logout</button>
    
    <div id="result" class="result"></div>

    <script>
        const API_BASE = 'http://localhost:5000/api/v1/user';
        
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
        }
        
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                fullName: document.getElementById('regFullName').value,
                email: document.getElementById('regEmail').value,
                password: document.getElementById('regPassword').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Registration successful!\n${JSON.stringify(data, null, 2)}`);
                } else {
                    showResult(`Registration failed: ${data.message}`, true);
                }
            } catch (error) {
                showResult(`Error: ${error.message}`, true);
            }
        });
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                email: document.getElementById('loginEmail').value,
                password: document.getElementById('loginPassword').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Login successful!\n${JSON.stringify(data, null, 2)}`);
                } else {
                    showResult(`Login failed: ${data.message}`, true);
                }
            } catch (error) {
                showResult(`Error: ${error.message}`, true);
            }
        });
        
        async function testProfile() {
            try {
                const response = await fetch(`${API_BASE}/me`, {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Profile fetched!\n${JSON.stringify(data, null, 2)}`);
                } else {
                    showResult(`Profile fetch failed: ${data.message}`, true);
                }
            } catch (error) {
                showResult(`Error: ${error.message}`, true);
            }
        }
        
        async function testLogout() {
            try {
                const response = await fetch(`${API_BASE}/logout`, {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Logout successful!\n${JSON.stringify(data, null, 2)}`);
                } else {
                    showResult(`Logout failed: ${data.message}`, true);
                }
            } catch (error) {
                showResult(`Error: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
